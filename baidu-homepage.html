<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度一下，你就知道</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .search-box {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            background-color: white;
        }
        .dark .search-box {
            background-color: #1f2937;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        .dark .search-box input {
            background-color: #1f2937;
            color: white;
        }
        .dark .search-box input::placeholder {
            color: #9ca3af;
        }
        .hot-search-item {
            transition: all 0.2s ease;
        }
        .hot-search-item:hover {
            color: #315efb;
            transform: translateX(4px);
        }
        .search-type-btn.active {
            background-color: #315efb;
            color: white;
        }
        .voice-search {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .voice-search:hover {
            color: #315efb;
            background-color: rgba(49, 94, 251, 0.1);
        }
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-3px); }
        }
        .animate-bounce {
            animation: bounce 1s infinite;
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col transition-colors duration-300">
    <header class="flex justify-end p-4">
        <nav class="flex space-x-4 items-center">
            <a href="#" class="text-sm hover:underline">新闻</a>
            <a href="#" class="text-sm hover:underline">hao123</a>
            <a href="#" class="text-sm hover:underline">地图</a>
            <a href="#" class="text-sm hover:underline">视频</a>
            <a href="#" class="text-sm hover:underline">贴吧</a>
            <button id="theme-toggle" class="p-1 rounded-full text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700">
                <i class="fas fa-moon"></i>
            </button>
            <button class="bg-blue-600 text-white px-4 py-1 rounded text-sm hover:bg-blue-700">登录</button>
        </nav>
    </header>

    <main class="flex-grow flex flex-col items-center justify-center px-4 -mt-16">
        <div class="mb-8">
            <img src="https://www.baidu.com/img/flexible/logo/pc/result.png" alt="百度Logo" class="h-20">
        </div>

        <div class="w-full max-w-2xl mb-8">
            <div class="relative search-box rounded-full bg-white flex items-center px-4 py-3">
                <input type="text" class="flex-grow outline-none px-2" placeholder="百度一下...">
                <button class="bg-blue-600 text-white rounded-full p-2 ml-2 hover:bg-blue-700">
                    <i class="fas fa-search"></i>
                </button>
                <div class="voice-search ml-2 text-gray-500 p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-microphone"></i>
                </div>
            </div>

            <div class="flex justify-center mt-4 space-x-2">
                <button class="search-type-btn active px-3 py-1 rounded-full text-sm" data-type="web">网页</button>
                <button class="search-type-btn px-3 py-1 rounded-full text-sm hover:bg-gray-100" data-type="image">图片</button>
                <button class="search-type-btn px-3 py-1 rounded-full text-sm hover:bg-gray-100" data-type="video">视频</button>
                <button class="search-type-btn px-3 py-1 rounded-full text-sm hover:bg-gray-100" data-type="zhidao">知道</button>
                <button class="search-type-btn px-3 py-1 rounded-full text-sm hover:bg-gray-100" data-type="tieba">贴吧</button>
            </div>
        </div>

        <div class="w-full max-w-xl">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <h3 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-3">百度热搜</h3>
                <div class="grid grid-cols-2 gap-4" id="hot-search-list">
                    <div class="hot-search-item cursor-pointer" data-query="2024年高考分数线">
                        <span class="text-red-500 mr-2">1</span>
                        <span>2024年高考分数线公布</span>
                    </div>
                    <div class="hot-search-item cursor-pointer" data-query="欧洲杯赛程">
                        <span class="text-red-500 mr-2">2</span>
                        <span>欧洲杯最新赛况</span>
                    </div>
                    <div class="hot-search-item cursor-pointer" data-query="新能源汽车补贴政策">
                        <span class="text-gray-500 mr-2">3</span>
                        <span>新能源汽车补贴政策</span>
                    </div>
                    <div class="hot-search-item cursor-pointer" data-query="夏季旅游推荐">
                        <span class="text-gray-500 mr-2">4</span>
                        <span>夏季旅游最佳目的地</span>
                    </div>
                    <div class="hot-search-item cursor-pointer" data-query="AI技术突破">
                        <span class="text-gray-500 mr-2">5</span>
                        <span>AI技术最新突破</span>
                    </div>
                    <div class="hot-search-item cursor-pointer" data-query="电影票房排行榜">
                        <span class="text-gray-500 mr-2">6</span>
                        <span>最新电影票房排行</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white dark:bg-gray-800 py-4 mt-auto">
        <div class="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>©2024 Baidu 使用百度前必读 意见反馈 京ICP证030173号</p>
        </div>
    </footer>

    <script>
        // 语音搜索功能
        const voiceSearchBtn = document.querySelector('.voice-search');
        const searchInput = document.querySelector('.search-box input');
        
        voiceSearchBtn.addEventListener('click', function() {
            // 显示语音识别状态
            this.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i>';
            this.style.color = '#315efb';
            
            // 模拟语音识别过程
            setTimeout(() => {
                // 随机示例语音指令
                const examples = [
                    "今天的天气怎么样",
                    "附近的美食推荐",
                    "2024年奥运会赛程",
                    "如何学习编程",
                    "最新的科技新闻"
                ];
                const randomExample = examples[Math.floor(Math.random() * examples.length)];
                
                // 显示识别结果
                searchInput.value = randomExample;
                this.innerHTML = '<i class="fas fa-microphone"></i>';
                this.style.color = '';
                
                // 添加语音波形动画
                const waves = document.createElement('div');
                waves.className = 'absolute -bottom-8 left-0 right-0 flex justify-center space-x-1';
                waves.innerHTML = `
                    <div class="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-1 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    <div class="w-1 h-3 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0.3s"></div>
                    <div class="w-1 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
                    <div class="w-1 h-1 bg-blue-400 rounded-full animate-bounce" style="animation-delay: 0.5s"></div>
                `;
                
                // 移除旧的波形元素
                const oldWaves = document.querySelector('.voice-waves');
                if (oldWaves) oldWaves.remove();
                
                waves.classList.add('voice-waves');
                this.parentNode.appendChild(waves);
                
                // 3秒后自动隐藏波形
                setTimeout(() => {
                    waves.style.opacity = '0';
                    setTimeout(() => waves.remove(), 300);
                }, 3000);
            }, 1500);
        });

        // 搜索类型切换
        const searchTypeBtns = document.querySelectorAll('.search-type-btn');
        const searchInput = document.querySelector('.search-box input');
        
        searchTypeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                searchTypeBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // 根据搜索类型更新placeholder
                const type = this.dataset.type;
                if (type === 'image') {
                    searchInput.placeholder = "搜索图片...";
                } else if (type === 'video') {
                    searchInput.placeholder = "搜索视频...";
                } else {
                    searchInput.placeholder = "百度一下...";
                }
            });
        });

        // 热搜点击事件
        document.querySelectorAll('.hot-search-item').forEach(item => {
            item.addEventListener('click', function() {
                const query = this.dataset.query;
                searchInput.value = query;
                
                // 模拟搜索按钮点击效果
                const searchBtn = document.querySelector('.search-box button');
                searchBtn.classList.add('bg-blue-700');
                setTimeout(() => {
                    searchBtn.classList.remove('bg-blue-700');
                    alert(`正在搜索: ${query}`);
                }, 200);
            });
        });

        // 深色模式切换
        const themeToggle = document.getElementById('theme-toggle');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // 初始化主题
        if (localStorage.getItem('color-theme') === 'dark' || (!localStorage.getItem('color-theme') && prefersDark)) {
            document.documentElement.classList.add('dark');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            document.documentElement.classList.remove('dark');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
        
        // 切换主题
        themeToggle.addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('color-theme', 'light');
                this.innerHTML = '<i class="fas fa-moon"></i>';
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('color-theme', 'dark');
                this.innerHTML = '<i class="fas fa-sun"></i>';
            }
        });

        // 图片搜索示例
        function showImageSearchExample() {
            alert('切换到图片搜索模式，可以上传图片或输入图片描述进行搜索');
        }
    </script>
</body>
</html>